<?php
/**
 * Simple test script for Plugin Ranker
 * 
 * This file can be run to test basic functionality of the Plugin Ranker plugin
 * Run this from WordPress admin or via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This file must be run within WordPress context');
}

/**
 * Plugin Ranker Test Class
 */
class PluginRanker_Test {
    
    /**
     * Run all tests
     */
    public static function run_tests() {
        echo "<h2>Plugin Ranker Test Results</h2>\n";
        
        $tests = array(
            'test_plugin_loaded',
            'test_classes_exist',
            'test_data_storage',
            'test_security_functions',
            'test_plugin_detection',
            'test_ranking_operations'
        );
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test) {
            $result = self::$test();
            if ($result) {
                echo "<p style='color: green;'>✓ {$test}: PASSED</p>\n";
                $passed++;
            } else {
                echo "<p style='color: red;'>✗ {$test}: FAILED</p>\n";
            }
        }
        
        echo "<h3>Summary: {$passed}/{$total} tests passed</h3>\n";
        
        if ($passed === $total) {
            echo "<p style='color: green; font-weight: bold;'>All tests passed! Plugin is working correctly.</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>Some tests failed. Please check the plugin installation.</p>\n";
        }
    }
    
    /**
     * Test if plugin is loaded
     */
    private static function test_plugin_loaded() {
        return class_exists('PluginRanker');
    }
    
    /**
     * Test if required classes exist
     */
    private static function test_classes_exist() {
        return class_exists('PluginRanker_DataStorage') && class_exists('PluginRanker_Security');
    }
    
    /**
     * Test data storage functionality
     */
    private static function test_data_storage() {
        try {
            // Test getting options
            $options = PluginRanker_DataStorage::get_options();
            if (!is_array($options)) {
                return false;
            }
            
            // Test getting rankings
            $rankings = PluginRanker_DataStorage::get_plugin_rankings();
            if (!is_array($rankings)) {
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Test security functions
     */
    private static function test_security_functions() {
        try {
            // Test nonce creation
            $nonce = PluginRanker_Security::create_nonce();
            if (empty($nonce)) {
                return false;
            }
            
            // Test capability check
            $can_manage = PluginRanker_Security::current_user_can_manage();
            // This should return boolean
            if (!is_bool($can_manage)) {
                return false;
            }
            
            // Test sanitization
            $test_rankings = array(
                'test-plugin/test.php' => '1',
                'invalid-plugin' => '2',
                'another-test/plugin.php' => 'invalid'
            );
            
            $sanitized = PluginRanker_Security::sanitize_rankings($test_rankings);
            if (!is_array($sanitized)) {
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Test plugin detection
     */
    private static function test_plugin_detection() {
        try {
            if (!function_exists('get_plugins')) {
                require_once ABSPATH . 'wp-admin/includes/plugin.php';
            }
            
            $plugins = get_plugins();
            if (!is_array($plugins)) {
                return false;
            }
            
            // Test if we can get plugins with rankings
            $plugin_ranker = PluginRanker::get_instance();
            $plugins_with_rankings = $plugin_ranker->get_plugins_with_rankings();
            
            if (!is_array($plugins_with_rankings)) {
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Test ranking operations
     */
    private static function test_ranking_operations() {
        try {
            // Get current rankings
            $original_rankings = PluginRanker_DataStorage::get_plugin_rankings();
            
            // Test setting a ranking
            $test_rankings = array('test-plugin/test.php' => 1);
            $result = PluginRanker_DataStorage::update_plugin_rankings($test_rankings);
            
            if (!$result) {
                return false;
            }
            
            // Test getting the ranking back
            $updated_rankings = PluginRanker_DataStorage::get_plugin_rankings();
            
            // Restore original rankings
            PluginRanker_DataStorage::update_plugin_rankings($original_rankings);
            
            return is_array($updated_rankings);
        } catch (Exception $e) {
            return false;
        }
    }
}

// Run tests if this file is accessed directly via admin
if (is_admin() && isset($_GET['run_plugin_ranker_test'])) {
    PluginRanker_Test::run_tests();
    exit;
}

/**
 * Add test link to admin menu (for testing purposes)
 */
function plugin_ranker_add_test_link() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'Plugin Ranker Test',
            'Plugin Ranker Test',
            'manage_options',
            'plugin-ranker-test',
            'plugin_ranker_test_page'
        );
    }
}

function plugin_ranker_test_page() {
    echo '<div class="wrap">';
    echo '<h1>Plugin Ranker Test</h1>';
    echo '<p>Click the button below to run tests for the Plugin Ranker plugin.</p>';
    echo '<p><a href="' . admin_url('tools.php?page=plugin-ranker-test&run_plugin_ranker_test=1') . '" class="button button-primary">Run Tests</a></p>';
    
    if (isset($_GET['run_plugin_ranker_test'])) {
        PluginRanker_Test::run_tests();
    }
    
    echo '</div>';
}

// Only add test menu in development/debug mode
if (defined('WP_DEBUG') && WP_DEBUG) {
    add_action('admin_menu', 'plugin_ranker_add_test_link');
}
