<?php
/**
 * Plugin Name: Plugin Ranker
 * Plugin URI: https://github.com/your-username/plugin-ranker
 * Description: A WordPress plugin that provides a simple Tools page for managing plugin rankings through drag-and-drop interface.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: plugin-ranker
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PLUGIN_RANKER_VERSION', '1.0.0');
define('PLUGIN_RANKER_PLUGIN_FILE', __FILE__);
define('PLUGIN_RANKER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PLUGIN_RANKER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PLUGIN_RANKER_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Plugin Ranker Class
 */
class PluginRanker {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Plugin options key
     */
    const OPTIONS_KEY = 'plugin_ranker_settings';
    
    /**
     * Get single instance of the class
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Load required classes
        $this->load_dependencies();

        // Activation and deactivation hooks
        register_activation_hook(PLUGIN_RANKER_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(PLUGIN_RANKER_PLUGIN_FILE, array($this, 'deactivate'));

        // Admin hooks
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // AJAX hooks
        add_action('wp_ajax_save_plugin_rankings', array($this, 'ajax_save_plugin_rankings'));
        add_action('wp_ajax_adjust_plugin_ranking', array($this, 'ajax_adjust_plugin_ranking'));

        // Initialize plugins page integration early
        add_action('admin_init', array('PluginRanker_PluginsPage', 'init'), 5);

        // Load text domain
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }

    /**
     * Load required dependencies
     */
    private function load_dependencies() {
        require_once PLUGIN_RANKER_PLUGIN_DIR . 'includes/class-data-storage.php';
        require_once PLUGIN_RANKER_PLUGIN_DIR . 'includes/class-security.php';
        require_once PLUGIN_RANKER_PLUGIN_DIR . 'includes/class-plugins-page.php';

        // Load test file in debug mode
        if (defined('WP_DEBUG') && WP_DEBUG && file_exists(PLUGIN_RANKER_PLUGIN_DIR . 'test-plugin.php')) {
            require_once PLUGIN_RANKER_PLUGIN_DIR . 'test-plugin.php';
        }
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Initialize default options using data storage class
        PluginRanker_DataStorage::initialize_options();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed (optional)
        // delete_option(self::OPTIONS_KEY);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_management_page(
            __('Plugin Ranker', 'plugin-ranker'),
            __('Plugin Ranker', 'plugin-ranker'),
            'manage_options',
            'plugin-ranker',
            array($this, 'admin_page_callback')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our admin page
        if ('tools_page_plugin-ranker' !== $hook) {
            return;
        }
        
        // Enqueue jQuery UI Sortable
        wp_enqueue_script('jquery-ui-sortable');
        
        // Enqueue our custom scripts and styles
        wp_enqueue_script(
            'plugin-ranker-admin',
            PLUGIN_RANKER_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'jquery-ui-sortable'),
            PLUGIN_RANKER_VERSION,
            true
        );
        
        wp_enqueue_style(
            'plugin-ranker-admin',
            PLUGIN_RANKER_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            PLUGIN_RANKER_VERSION
        );
        
        // Localize script for AJAX
        wp_localize_script('plugin-ranker-admin', 'pluginRankerAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => PluginRanker_Security::create_nonce(),
            'messages' => array(
                'saving' => __('Saving...', 'plugin-ranker'),
                'saved' => __('Rankings saved successfully!', 'plugin-ranker'),
                'error' => __('Error saving rankings. Please try again.', 'plugin-ranker')
            )
        ));
    }
    
    /**
     * Admin page callback
     */
    public function admin_page_callback() {
        include_once PLUGIN_RANKER_PLUGIN_DIR . 'includes/admin-page.php';
    }
    
    /**
     * AJAX handler for saving plugin rankings
     */
    public function ajax_save_plugin_rankings() {
        // Check rate limiting
        if (!PluginRanker_Security::check_rate_limit('save_rankings', 20, 60)) {
            wp_send_json_error(array('message' => __('Too many requests. Please try again later.', 'plugin-ranker')));
            return;
        }

        // Verify security
        $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
        $security_check = PluginRanker_Security::verify_ajax_request($nonce);

        if (is_wp_error($security_check)) {
            PluginRanker_Security::log_security_event('ajax_security_failure', array(
                'error' => $security_check->get_error_code(),
                'message' => $security_check->get_error_message()
            ));
            wp_send_json_error(array('message' => $security_check->get_error_message()));
            return;
        }

        // Get and sanitize the rankings data
        $rankings = isset($_POST['rankings']) ? $_POST['rankings'] : array();
        $sanitized_rankings = PluginRanker_Security::sanitize_rankings($rankings);

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Plugin Ranker AJAX Save - Original rankings: ' . print_r($rankings, true));
            error_log('Plugin Ranker AJAX Save - Sanitized rankings: ' . print_r($sanitized_rankings, true));
        }

        // Log the action
        PluginRanker_Security::log_security_event('rankings_updated', array(
            'original_count' => count($rankings),
            'sanitized_count' => count($sanitized_rankings)
        ));

        // Update rankings using data storage class
        $result = PluginRanker_DataStorage::update_plugin_rankings($sanitized_rankings);

        // Debug: Verify the save
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $saved_rankings = PluginRanker_DataStorage::get_plugin_rankings();
            error_log('Plugin Ranker AJAX Save - Saved rankings verification: ' . print_r($saved_rankings, true));
        }

        if ($result) {
            wp_send_json_success(array('message' => __('Rankings saved successfully!', 'plugin-ranker')));
        } else {
            wp_send_json_error(array('message' => __('Error saving rankings', 'plugin-ranker')));
        }
    }
    
    /**
     * AJAX handler for adjusting individual plugin ranking
     */
    public function ajax_adjust_plugin_ranking() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'plugin_ranker_adjust')) {
            wp_die(__('Security check failed', 'plugin-ranker'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'plugin-ranker'));
        }

        $plugin_file = isset($_POST['plugin_file']) ? sanitize_text_field($_POST['plugin_file']) : '';
        $direction = isset($_POST['direction']) ? sanitize_text_field($_POST['direction']) : '';

        if (empty($plugin_file) || !in_array($direction, array('up', 'down'))) {
            wp_send_json_error(array('message' => __('Invalid parameters', 'plugin-ranker')));
            return;
        }

        // Validate plugin file
        if (!PluginRanker_Security::validate_plugin_file($plugin_file)) {
            wp_send_json_error(array('message' => __('Invalid plugin file', 'plugin-ranker')));
            return;
        }

        $rankings = PluginRanker_DataStorage::get_plugin_rankings();
        $current_rank = isset($rankings[$plugin_file]) ? $rankings[$plugin_file] : 0;

        if ($current_rank <= 0) {
            wp_send_json_error(array('message' => __('Plugin is not ranked', 'plugin-ranker')));
            return;
        }

        // Adjust ranking
        if ($direction === 'up' && $current_rank > 1) {
            // Find plugin with rank - 1 and swap
            $target_rank = $current_rank - 1;
            $target_plugin = array_search($target_rank, $rankings);

            if ($target_plugin !== false) {
                $rankings[$target_plugin] = $current_rank;
                $rankings[$plugin_file] = $target_rank;
            }
        } elseif ($direction === 'down') {
            // Find plugin with rank + 1 and swap
            $target_rank = $current_rank + 1;
            $target_plugin = array_search($target_rank, $rankings);

            if ($target_plugin !== false) {
                $rankings[$target_plugin] = $current_rank;
                $rankings[$plugin_file] = $target_rank;
            }
        }

        // Save updated rankings
        $result = PluginRanker_DataStorage::update_plugin_rankings($rankings);

        if ($result) {
            wp_send_json_success(array('message' => __('Ranking adjusted successfully!', 'plugin-ranker')));
        } else {
            wp_send_json_error(array('message' => __('Error adjusting ranking', 'plugin-ranker')));
        }
    }

    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'plugin-ranker',
            false,
            dirname(PLUGIN_RANKER_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Get plugin rankings
     */
    public function get_plugin_rankings() {
        return PluginRanker_DataStorage::get_plugin_rankings();
    }
    
    /**
     * Get all installed plugins with ranking info
     */
    public function get_plugins_with_rankings() {
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        $all_plugins = get_plugins();
        $rankings = $this->get_plugin_rankings();
        $plugins_with_rankings = array();
        
        foreach ($all_plugins as $plugin_file => $plugin_data) {
            $rank = isset($rankings[$plugin_file]) ? intval($rankings[$plugin_file]) : 0;
            
            $plugins_with_rankings[] = array(
                'file' => $plugin_file,
                'name' => $plugin_data['Name'],
                'description' => $plugin_data['Description'],
                'version' => $plugin_data['Version'],
                'author' => $plugin_data['Author'],
                'active' => is_plugin_active($plugin_file),
                'rank' => $rank
            );
        }
        
        // Sort by rank (0 means unranked, should go to bottom)
        usort($plugins_with_rankings, function($a, $b) {
            if ($a['rank'] == 0 && $b['rank'] == 0) {
                return strcmp($a['name'], $b['name']);
            }
            if ($a['rank'] == 0) return 1;
            if ($b['rank'] == 0) return -1;
            return $a['rank'] - $b['rank'];
        });
        
        return $plugins_with_rankings;
    }
}

// Initialize the plugin
function plugin_ranker_init() {
    return PluginRanker::get_instance();
}

// Start the plugin
plugin_ranker_init();
