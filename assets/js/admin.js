/**
 * Plugin Ranker Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Plugin Ranker Admin Object
    var PluginRankerAdmin = {
        
        /**
         * Initialize the admin functionality
         */
        init: function() {
            this.checkDependencies();
            this.initSortable();
            this.bindEvents();
        },
        
        /**
         * Check if required dependencies are loaded
         */
        checkDependencies: function() {
            if (typeof $ === 'undefined') {
                console.error('Plugin Ranker: jQuery is not loaded');
                return false;
            }
            
            if (typeof $.ui === 'undefined' || typeof $.ui.sortable === 'undefined') {
                console.error('Plugin Ranker: jQuery UI Sortable is not loaded');
                this.showError('jQuery UI Sortable is required but not loaded. Please contact your administrator.');
                return false;
            }
            
            if (typeof pluginRankerAjax === 'undefined') {
                console.error('Plugin Ranker: AJAX configuration is not loaded');
                this.showError('Plugin configuration is missing. Please refresh the page.');
                return false;
            }
            
            return true;
        },
        
        /**
         * Initialize sortable functionality
         */
        initSortable: function() {
            var self = this;
            var $sortable = $('#plugin-ranker-sortable');
            
            if ($sortable.length === 0) {
                return;
            }
            
            $sortable.sortable({
                handle: '.drag-handle',
                cursor: 'move',
                placeholder: 'plugin-row-placeholder',
                tolerance: 'pointer',
                distance: 5,
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    $helper.addClass('ui-sortable-helper');
                    return $helper;
                },
                start: function(e, ui) {
                    ui.placeholder.height(ui.item.height());
                    self.showStatus('saving');
                },
                stop: function(e, ui) {
                    self.updateRankNumbers();
                    self.savePluginRankings();
                },
                update: function(e, ui) {
                    // This fires when the order actually changes
                }
            });
            
            // Add visual feedback for drag handles
            $('.drag-handle').on('mouseenter', function() {
                $(this).closest('tr').addClass('drag-ready');
            }).on('mouseleave', function() {
                $(this).closest('tr').removeClass('drag-ready');
            });
        },
        
        /**
         * Bind additional events
         */
        bindEvents: function() {
            var self = this;
            
            // Handle keyboard accessibility
            $('.drag-handle').on('keydown', function(e) {
                var $row = $(this).closest('tr');
                var $tbody = $row.closest('tbody');
                
                switch(e.which) {
                    case 38: // Up arrow
                        e.preventDefault();
                        self.moveRow($row, 'up');
                        break;
                    case 40: // Down arrow
                        e.preventDefault();
                        self.moveRow($row, 'down');
                        break;
                    case 13: // Enter
                    case 32: // Space
                        e.preventDefault();
                        $(this).trigger('click');
                        break;
                }
            });
            
            // Add tabindex for accessibility
            $('.drag-handle').attr('tabindex', '0');
            $('.drag-handle').attr('role', 'button');
            $('.drag-handle').attr('aria-label', 'Drag to reorder plugin');
        },
        
        /**
         * Move row up or down (for keyboard accessibility)
         */
        moveRow: function($row, direction) {
            var $target;
            
            if (direction === 'up') {
                $target = $row.prev();
                if ($target.length) {
                    $row.insertBefore($target);
                    this.updateRankNumbers();
                    this.savePluginRankings();
                }
            } else if (direction === 'down') {
                $target = $row.next();
                if ($target.length) {
                    $row.insertAfter($target);
                    this.updateRankNumbers();
                    this.savePluginRankings();
                }
            }
        },
        
        /**
         * Update rank numbers in the UI
         */
        updateRankNumbers: function() {
            $('#plugin-ranker-sortable tr').each(function(index) {
                $(this).find('.rank-number').text(index + 1);
            });
        },
        
        /**
         * Save plugin rankings via AJAX
         */
        savePluginRankings: function() {
            var self = this;
            var rankings = {};
            
            // Collect current order
            $('#plugin-ranker-sortable tr').each(function(index) {
                var pluginFile = $(this).data('plugin-file');
                if (pluginFile) {
                    rankings[pluginFile] = index + 1;
                }
            });
            
            // Send AJAX request
            $.ajax({
                url: pluginRankerAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'save_plugin_rankings',
                    nonce: pluginRankerAjax.nonce,
                    rankings: rankings
                },
                timeout: 10000,
                success: function(response) {
                    if (response.success) {
                        self.showStatus('saved');
                        self.hideStatusAfterDelay();
                    } else {
                        var message = response.data && response.data.message ? 
                            response.data.message : pluginRankerAjax.messages.error;
                        self.showStatus('error', message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Plugin Ranker AJAX Error:', status, error);
                    self.showStatus('error');
                }
            });
        },
        
        /**
         * Show status message
         */
        showStatus: function(type, customMessage) {
            var message;
            var className;
            
            switch(type) {
                case 'saving':
                    message = pluginRankerAjax.messages.saving;
                    className = 'saving';
                    break;
                case 'saved':
                    message = pluginRankerAjax.messages.saved;
                    className = 'saved';
                    break;
                case 'error':
                    message = customMessage || pluginRankerAjax.messages.error;
                    className = 'error';
                    break;
                default:
                    message = customMessage || '';
                    className = type;
            }
            
            $('#save-status').html('<span class="' + className + '">' + message + '</span>');
        },
        
        /**
         * Hide status message after delay
         */
        hideStatusAfterDelay: function(delay) {
            delay = delay || 3000;
            setTimeout(function() {
                $('#save-status').html('');
            }, delay);
        },
        
        /**
         * Show error message to user
         */
        showError: function(message) {
            var $container = $('.plugin-ranker-container');
            if ($container.length) {
                $container.prepend(
                    '<div class="notice notice-error is-dismissible">' +
                    '<p>' + message + '</p>' +
                    '</div>'
                );
            }
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        PluginRankerAdmin.init();
    });
    
    // Make PluginRankerAdmin globally available for debugging
    window.PluginRankerAdmin = PluginRankerAdmin;
    
})(jQuery);
