/**
 * Plugin Ranker Admin Styles
 */

.plugin-ranker-container {
    max-width: 100%;
    margin: 20px 0;
}

.plugin-ranker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.plugin-ranker-header p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.plugin-ranker-status {
    min-width: 200px;
    text-align: right;
}

.plugin-ranker-instructions {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.plugin-ranker-instructions ul {
    margin: 10px 0 0 20px;
}

.plugin-ranker-instructions li {
    margin-bottom: 5px;
    font-size: 13px;
    color: #666;
}

.plugin-ranker-table-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

/* Table Styles */
.plugin-ranker-sortable {
    cursor: default;
}

.plugin-ranker-sortable .plugin-row {
    transition: background-color 0.2s ease;
}

.plugin-ranker-sortable .plugin-row:hover {
    background-color: #f5f5f5;
}

.plugin-ranker-sortable .plugin-row.ui-sortable-helper {
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    border: 1px solid #ddd;
}

.plugin-row-placeholder {
    background-color: #e3f2fd !important;
    border: 2px dashed #2196f3 !important;
    height: 60px !important;
}

.plugin-row-placeholder td {
    border: none !important;
}

/* Column Styles */
.rank-column {
    width: 80px;
    text-align: center;
}

.plugin-column {
    width: 25%;
    min-width: 200px;
}

.description-column {
    width: 35%;
}

.version-column {
    width: 10%;
    text-align: center;
}

.author-column {
    width: 15%;
}

.status-column {
    width: 10%;
    text-align: center;
}

/* Rank Column */
.rank-number {
    display: inline-block;
    font-weight: bold;
    font-size: 16px;
    color: #0073aa;
    margin-right: 10px;
}

.drag-handle {
    cursor: move;
    color: #666;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.drag-handle:hover {
    background-color: #0073aa;
    color: #fff;
}

.drag-handle .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Plugin Column */
.plugin-name {
    display: block;
    font-size: 14px;
    margin-bottom: 3px;
}

.plugin-file {
    font-size: 12px;
    color: #666;
    font-family: monospace;
}

/* Description Column */
.plugin-description {
    font-size: 13px;
    line-height: 1.4;
    color: #555;
}

/* Status Column */
.plugin-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active {
    background-color: #46b450;
    color: #fff;
}

.status-inactive {
    background-color: #ddd;
    color: #666;
}

/* Active Plugin Row Highlighting */
.active-plugin {
    background-color: #f0f8f0;
}

.active-plugin:hover {
    background-color: #e8f5e8;
}

/* Status Messages */
#save-status .saving {
    color: #0073aa;
    font-weight: bold;
}

#save-status .saved {
    color: #46b450;
    font-weight: bold;
}

#save-status .error {
    color: #dc3232;
    font-weight: bold;
}

/* Footer */
.plugin-ranker-footer {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.plugin-ranker-footer .description {
    margin: 0;
    font-size: 13px;
    color: #666;
    font-style: italic;
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
    .description-column {
        width: 30%;
    }
    
    .author-column {
        width: 20%;
    }
}

@media screen and (max-width: 900px) {
    .plugin-ranker-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .plugin-ranker-status {
        margin-top: 10px;
        min-width: auto;
        text-align: left;
    }
    
    .description-column,
    .author-column {
        display: none;
    }
    
    .plugin-column {
        width: 50%;
    }
    
    .version-column {
        width: 15%;
    }
    
    .status-column {
        width: 15%;
    }
}

@media screen and (max-width: 600px) {
    .rank-column {
        width: 60px;
    }
    
    .plugin-column {
        width: 60%;
    }
    
    .version-column {
        display: none;
    }
    
    .status-column {
        width: 25%;
    }
    
    .drag-handle {
        padding: 3px;
    }
    
    .rank-number {
        font-size: 14px;
        margin-right: 5px;
    }
}
