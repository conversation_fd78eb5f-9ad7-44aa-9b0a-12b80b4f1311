# Plugin Ranker

A WordPress plugin that provides a simple Tools page for managing plugin rankings through drag-and-drop interface.

## Description

Plugin Ranker allows WordPress administrators to assign custom rankings to installed plugins through an intuitive drag-and-drop interface. The plugin adds a new page under Tools → Plugin Ranker where you can easily reorder plugins and assign them numerical rankings.

## Features

- **Drag & Drop Interface**: Intuitive reordering of plugins using jQuery UI Sortable
- **Auto-ranking**: Automatic assignment of ranks (1, 2, 3...) based on drag order
- **AJAX Saving**: Real-time saving of changes without page reload
- **Security**: Proper WordPress nonces, capability checks, and input sanitization
- **Responsive Design**: Works on desktop and mobile devices
- **Clean UI**: Follows WordPress admin design standards

## Requirements

- WordPress 6.0 or higher
- PHP 7.4 or higher
- JavaScript enabled in browser
- `manage_options` capability (Administrator role)

## Installation

### Manual Installation

1. Download the plugin files
2. Upload the `plugin-ranker` folder to `/wp-content/plugins/`
3. Activate the plugin through the 'Plugins' menu in WordPress
4. Navigate to Tools → Plugin Ranker to start ranking your plugins

### Via WordPress Admin

1. Go to Plugins → Add New
2. Upload the plugin zip file
3. Activate the plugin
4. Navigate to Tools → Plugin Ranker

## Usage

1. **Access the Plugin**: Go to Tools → Plugin Ranker in your WordPress admin
2. **Reorder Plugins**: Drag plugins up or down using the drag handle (≡ icon)
3. **Auto-save**: Rankings are automatically saved when you drop a plugin
4. **View Status**: Active plugins are highlighted in green
5. **Check Progress**: Save status is displayed in the top-right corner

### Plugin Information Displayed

- **Rank**: Numerical position (1, 2, 3...)
- **Plugin Name**: Name and file path
- **Description**: Plugin description
- **Version**: Current version number
- **Author**: Plugin author/developer
- **Status**: Active or Inactive

## Technical Details

### Data Storage

- Plugin rankings are stored in the WordPress `wp_options` table
- Option key: `plugin_ranker_settings`
- Data is automatically sanitized and validated before storage

### Security Features

- WordPress nonces for CSRF protection
- Capability checks (`manage_options` required)
- Input sanitization and validation
- Rate limiting for AJAX requests
- Security event logging (when WP_DEBUG is enabled)

### File Structure

```
plugin-ranker/
├── plugin-ranker.php          # Main plugin file
├── includes/
│   ├── admin-page.php          # Admin page template
│   ├── class-data-storage.php  # Data storage handler
│   └── class-security.php      # Security utilities
├── assets/
│   ├── css/
│   │   └── admin.css          # Admin styles
│   └── js/
│       └── admin.js           # Admin JavaScript
└── languages/                 # Translation files (future)
```

## Hooks and Filters

### Actions

- `plugin_ranker_rankings_updated` - Fired when rankings are updated
- `plugin_ranker_plugin_activated` - Fired on plugin activation
- `plugin_ranker_plugin_deactivated` - Fired on plugin deactivation

### Filters

- `plugin_ranker_default_options` - Filter default plugin options
- `plugin_ranker_sanitize_rankings` - Filter rankings before saving

## API Reference

### Main Class: `PluginRanker`

#### Methods

- `get_instance()` - Get singleton instance
- `get_plugin_rankings()` - Get current plugin rankings
- `get_plugins_with_rankings()` - Get all plugins with ranking info

### Data Storage: `PluginRanker_DataStorage`

#### Methods

- `get_plugin_rankings()` - Get rankings array
- `update_plugin_rankings($rankings)` - Update rankings
- `get_plugin_ranking($plugin_file)` - Get single plugin rank
- `set_plugin_ranking($plugin_file, $rank)` - Set single plugin rank
- `cleanup_rankings()` - Remove rankings for non-existent plugins

### Security: `PluginRanker_Security`

#### Methods

- `verify_nonce($nonce)` - Verify security nonce
- `current_user_can_manage()` - Check user capabilities
- `sanitize_rankings($rankings)` - Sanitize rankings data
- `validate_plugin_file($plugin_file)` - Validate plugin file

## Troubleshooting

### Common Issues

**Drag and drop not working**
- Ensure JavaScript is enabled
- Check browser console for errors
- Verify jQuery UI Sortable is loaded

**Rankings not saving**
- Check user has `manage_options` capability
- Verify AJAX requests are not blocked
- Check for plugin conflicts

**Plugin not appearing in Tools menu**
- Ensure plugin is activated
- Check user has administrator role
- Clear any caching plugins

### Debug Mode

Enable WordPress debug mode to see detailed error logs:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Changelog

### Version 1.0.0
- Initial release
- Drag and drop plugin ranking
- AJAX auto-save functionality
- Security implementation
- Responsive design

## Support

For support, please:

1. Check the troubleshooting section above
2. Enable debug mode and check error logs
3. Create an issue on the plugin repository

## License

This plugin is licensed under the GPL v2 or later.

## Credits

- Built with WordPress best practices
- Uses jQuery UI Sortable for drag-and-drop
- Follows WordPress Coding Standards
