<?php
/**
 * Data Storage Class for Plugin Ranker
 * 
 * Handles all data storage operations using WordPress wp_options table
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginRanker_DataStorage {
    
    /**
     * Options key for storing plugin rankings
     */
    const OPTIONS_KEY = 'plugin_ranker_settings';
    
    /**
     * Default options structure
     */
    private static $default_options = array(
        'plugin_rankings' => array(),
        'version' => PLUGIN_RANKER_VERSION,
        'last_updated' => 0
    );
    
    /**
     * Get all plugin ranker options
     * 
     * @return array
     */
    public static function get_options() {
        $options = get_option(self::OPTIONS_KEY, array());
        
        // Merge with defaults to ensure all keys exist
        $options = wp_parse_args($options, self::$default_options);
        
        return $options;
    }
    
    /**
     * Update plugin ranker options
     * 
     * @param array $options
     * @return bool
     */
    public static function update_options($options) {
        // Sanitize options before saving
        $sanitized_options = self::sanitize_options($options);
        
        // Add timestamp
        $sanitized_options['last_updated'] = time();
        
        return update_option(self::OPTIONS_KEY, $sanitized_options);
    }
    
    /**
     * Get plugin rankings
     * 
     * @return array
     */
    public static function get_plugin_rankings() {
        $options = self::get_options();
        return isset($options['plugin_rankings']) ? $options['plugin_rankings'] : array();
    }
    
    /**
     * Update plugin rankings
     * 
     * @param array $rankings
     * @return bool
     */
    public static function update_plugin_rankings($rankings) {
        $options = self::get_options();
        $options['plugin_rankings'] = self::sanitize_rankings($rankings);
        
        return self::update_options($options);
    }
    
    /**
     * Get ranking for a specific plugin
     * 
     * @param string $plugin_file
     * @return int
     */
    public static function get_plugin_ranking($plugin_file) {
        $rankings = self::get_plugin_rankings();
        return isset($rankings[$plugin_file]) ? intval($rankings[$plugin_file]) : 0;
    }
    
    /**
     * Set ranking for a specific plugin
     * 
     * @param string $plugin_file
     * @param int $rank
     * @return bool
     */
    public static function set_plugin_ranking($plugin_file, $rank) {
        $rankings = self::get_plugin_rankings();
        $rankings[sanitize_text_field($plugin_file)] = intval($rank);
        
        return self::update_plugin_rankings($rankings);
    }
    
    /**
     * Remove ranking for a specific plugin
     * 
     * @param string $plugin_file
     * @return bool
     */
    public static function remove_plugin_ranking($plugin_file) {
        $rankings = self::get_plugin_rankings();
        
        if (isset($rankings[$plugin_file])) {
            unset($rankings[$plugin_file]);
            return self::update_plugin_rankings($rankings);
        }
        
        return true;
    }
    
    /**
     * Clear all plugin rankings
     * 
     * @return bool
     */
    public static function clear_all_rankings() {
        return self::update_plugin_rankings(array());
    }
    
    /**
     * Initialize default options
     * 
     * @return bool
     */
    public static function initialize_options() {
        if (!get_option(self::OPTIONS_KEY)) {
            return add_option(self::OPTIONS_KEY, self::$default_options);
        }
        return true;
    }
    
    /**
     * Delete all plugin ranker options
     * 
     * @return bool
     */
    public static function delete_options() {
        return delete_option(self::OPTIONS_KEY);
    }
    
    /**
     * Sanitize options array
     * 
     * @param array $options
     * @return array
     */
    private static function sanitize_options($options) {
        $sanitized = array();
        
        // Sanitize plugin rankings
        if (isset($options['plugin_rankings']) && is_array($options['plugin_rankings'])) {
            $sanitized['plugin_rankings'] = self::sanitize_rankings($options['plugin_rankings']);
        } else {
            $sanitized['plugin_rankings'] = array();
        }
        
        // Sanitize version
        if (isset($options['version'])) {
            $sanitized['version'] = sanitize_text_field($options['version']);
        } else {
            $sanitized['version'] = PLUGIN_RANKER_VERSION;
        }
        
        // Sanitize last_updated
        if (isset($options['last_updated'])) {
            $sanitized['last_updated'] = intval($options['last_updated']);
        } else {
            $sanitized['last_updated'] = time();
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize rankings array
     *
     * @param array $rankings
     * @return array
     */
    private static function sanitize_rankings($rankings) {
        // Use security class for sanitization if available
        if (class_exists('PluginRanker_Security')) {
            return PluginRanker_Security::sanitize_rankings($rankings);
        }

        // Fallback sanitization
        $sanitized = array();

        if (!is_array($rankings)) {
            return $sanitized;
        }

        foreach ($rankings as $plugin_file => $rank) {
            $clean_plugin_file = sanitize_text_field($plugin_file);
            $clean_rank = intval($rank);

            // Only store valid plugin files and positive ranks
            if (!empty($clean_plugin_file) && $clean_rank > 0) {
                $sanitized[$clean_plugin_file] = $clean_rank;
            }
        }

        return $sanitized;
    }
    
    /**
     * Validate plugin file exists
     * 
     * @param string $plugin_file
     * @return bool
     */
    public static function validate_plugin_file($plugin_file) {
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        $all_plugins = get_plugins();
        return isset($all_plugins[$plugin_file]);
    }
    
    /**
     * Clean up rankings for non-existent plugins
     * 
     * @return bool
     */
    public static function cleanup_rankings() {
        $rankings = self::get_plugin_rankings();
        $cleaned_rankings = array();
        
        foreach ($rankings as $plugin_file => $rank) {
            if (self::validate_plugin_file($plugin_file)) {
                $cleaned_rankings[$plugin_file] = $rank;
            }
        }
        
        // Only update if there were changes
        if (count($cleaned_rankings) !== count($rankings)) {
            return self::update_plugin_rankings($cleaned_rankings);
        }
        
        return true;
    }
    
    /**
     * Get plugin ranker statistics
     * 
     * @return array
     */
    public static function get_statistics() {
        $options = self::get_options();
        $rankings = $options['plugin_rankings'];
        
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        $all_plugins = get_plugins();
        $total_plugins = count($all_plugins);
        $ranked_plugins = count($rankings);
        $unranked_plugins = $total_plugins - $ranked_plugins;
        
        return array(
            'total_plugins' => $total_plugins,
            'ranked_plugins' => $ranked_plugins,
            'unranked_plugins' => $unranked_plugins,
            'last_updated' => $options['last_updated'],
            'version' => $options['version']
        );
    }
    
    /**
     * Export rankings data
     * 
     * @return array
     */
    public static function export_rankings() {
        return array(
            'plugin_ranker_export' => true,
            'export_date' => current_time('mysql'),
            'wordpress_version' => get_bloginfo('version'),
            'plugin_ranker_version' => PLUGIN_RANKER_VERSION,
            'rankings' => self::get_plugin_rankings()
        );
    }
    
    /**
     * Import rankings data
     * 
     * @param array $data
     * @return bool|WP_Error
     */
    public static function import_rankings($data) {
        // Validate import data
        if (!is_array($data) || !isset($data['plugin_ranker_export']) || !$data['plugin_ranker_export']) {
            return new WP_Error('invalid_import', __('Invalid import data format', 'plugin-ranker'));
        }
        
        if (!isset($data['rankings']) || !is_array($data['rankings'])) {
            return new WP_Error('invalid_rankings', __('No rankings data found in import', 'plugin-ranker'));
        }
        
        // Import rankings
        $result = self::update_plugin_rankings($data['rankings']);
        
        if ($result) {
            return true;
        } else {
            return new WP_Error('import_failed', __('Failed to import rankings', 'plugin-ranker'));
        }
    }
}
