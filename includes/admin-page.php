<?php
/**
 * Admin page template for Plugin Ranker
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get the plugin instance
$plugin_ranker = PluginRanker::get_instance();
$plugins = $plugin_ranker->get_plugins_with_rankings();
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <div class="plugin-ranker-container">
        <div class="plugin-ranker-header">
            <p><?php _e('Drag and drop plugins to reorder them. Rankings will be saved automatically.', 'plugin-ranker'); ?></p>
            <div class="plugin-ranker-status">
                <span id="save-status"></span>
            </div>
        </div>
        
        <?php if (empty($plugins)): ?>
            <div class="notice notice-info">
                <p><?php _e('No plugins found.', 'plugin-ranker'); ?></p>
            </div>
        <?php else: ?>
            <div class="plugin-ranker-list-container">
                <div class="plugin-ranker-instructions">
                    <p><strong><?php _e('Instructions:', 'plugin-ranker'); ?></strong></p>
                    <ul>
                        <li><?php _e('Drag plugins up or down to change their ranking', 'plugin-ranker'); ?></li>
                        <li><?php _e('Rankings are automatically assigned based on position (1, 2, 3...)', 'plugin-ranker'); ?></li>
                        <li><?php _e('Active plugins are highlighted in green', 'plugin-ranker'); ?></li>
                        <li><?php _e('Changes are saved automatically when you drop a plugin', 'plugin-ranker'); ?></li>
                    </ul>
                </div>
                
                <div class="plugin-ranker-table-container">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th class="rank-column"><?php _e('Rank', 'plugin-ranker'); ?></th>
                                <th class="plugin-column"><?php _e('Plugin', 'plugin-ranker'); ?></th>
                                <th class="description-column"><?php _e('Description', 'plugin-ranker'); ?></th>
                                <th class="version-column"><?php _e('Version', 'plugin-ranker'); ?></th>
                                <th class="author-column"><?php _e('Author', 'plugin-ranker'); ?></th>
                                <th class="status-column"><?php _e('Status', 'plugin-ranker'); ?></th>
                            </tr>
                        </thead>
                        <tbody id="plugin-ranker-sortable" class="plugin-ranker-sortable">
                            <?php foreach ($plugins as $index => $plugin): ?>
                                <tr class="plugin-row <?php echo $plugin['active'] ? 'active-plugin' : 'inactive-plugin'; ?>" 
                                    data-plugin-file="<?php echo esc_attr($plugin['file']); ?>">
                                    <td class="rank-column">
                                        <span class="rank-number"><?php echo $index + 1; ?></span>
                                        <span class="drag-handle" title="<?php _e('Drag to reorder', 'plugin-ranker'); ?>">
                                            <span class="dashicons dashicons-menu"></span>
                                        </span>
                                    </td>
                                    <td class="plugin-column">
                                        <strong class="plugin-name"><?php echo esc_html($plugin['name']); ?></strong>
                                        <div class="plugin-file"><?php echo esc_html($plugin['file']); ?></div>
                                    </td>
                                    <td class="description-column">
                                        <div class="plugin-description">
                                            <?php echo wp_kses_post($plugin['description']); ?>
                                        </div>
                                    </td>
                                    <td class="version-column">
                                        <?php echo esc_html($plugin['version']); ?>
                                    </td>
                                    <td class="author-column">
                                        <?php echo wp_kses_post($plugin['author']); ?>
                                    </td>
                                    <td class="status-column">
                                        <span class="plugin-status <?php echo $plugin['active'] ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo $plugin['active'] ? __('Active', 'plugin-ranker') : __('Inactive', 'plugin-ranker'); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="plugin-ranker-footer">
            <p class="description">
                <?php _e('Plugin rankings are stored in your WordPress database and will persist across sessions.', 'plugin-ranker'); ?>
            </p>
        </div>
    </div>
</div>

<script type="text/javascript">
// Ensure jQuery and jQuery UI are loaded
jQuery(document).ready(function($) {
    if (typeof $.ui === 'undefined' || typeof $.ui.sortable === 'undefined') {
        console.error('Plugin Ranker: jQuery UI Sortable is not loaded');
        return;
    }
    
    // Initialize sortable functionality
    $('#plugin-ranker-sortable').sortable({
        handle: '.drag-handle',
        cursor: 'move',
        placeholder: 'plugin-row-placeholder',
        helper: function(e, tr) {
            var $originals = tr.children();
            var $helper = tr.clone();
            $helper.children().each(function(index) {
                $(this).width($originals.eq(index).width());
            });
            return $helper;
        },
        start: function(e, ui) {
            ui.placeholder.height(ui.item.height());
            $('#save-status').html('<span class="saving">' + pluginRankerAjax.messages.saving + '</span>');
        },
        stop: function(e, ui) {
            // Update rank numbers
            updateRankNumbers();
            
            // Save the new order
            savePluginRankings();
        }
    });
    
    function updateRankNumbers() {
        $('#plugin-ranker-sortable tr').each(function(index) {
            $(this).find('.rank-number').text(index + 1);
        });
    }
    
    function savePluginRankings() {
        var rankings = {};
        
        $('#plugin-ranker-sortable tr').each(function(index) {
            var pluginFile = $(this).data('plugin-file');
            rankings[pluginFile] = index + 1;
        });
        
        $.ajax({
            url: pluginRankerAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'save_plugin_rankings',
                nonce: pluginRankerAjax.nonce,
                rankings: rankings
            },
            success: function(response) {
                if (response.success) {
                    $('#save-status').html('<span class="saved">' + pluginRankerAjax.messages.saved + '</span>');
                    setTimeout(function() {
                        $('#save-status').html('');
                    }, 3000);
                } else {
                    $('#save-status').html('<span class="error">' + pluginRankerAjax.messages.error + '</span>');
                }
            },
            error: function() {
                $('#save-status').html('<span class="error">' + pluginRankerAjax.messages.error + '</span>');
            }
        });
    }
});
</script>
