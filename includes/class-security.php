<?php
/**
 * Security Class for Plugin Ranker
 * 
 * Handles all security-related functionality including nonces, capability checks, and input validation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginRanker_Security {
    
    /**
     * Nonce action for plugin rankings
     */
    const NONCE_ACTION = 'plugin_ranker_nonce';
    
    /**
     * Required capability for plugin management
     */
    const REQUIRED_CAPABILITY = 'manage_options';
    
    /**
     * Verify nonce for AJAX requests
     * 
     * @param string $nonce
     * @return bool
     */
    public static function verify_nonce($nonce) {
        return wp_verify_nonce($nonce, self::NONCE_ACTION);
    }
    
    /**
     * Create nonce for forms and AJAX
     * 
     * @return string
     */
    public static function create_nonce() {
        return wp_create_nonce(self::NONCE_ACTION);
    }
    
    /**
     * Check if current user has required capabilities
     * 
     * @return bool
     */
    public static function current_user_can_manage() {
        return current_user_can(self::REQUIRED_CAPABILITY);
    }
    
    /**
     * Verify user capabilities and nonce for AJAX requests
     * 
     * @param string $nonce
     * @return bool|WP_Error
     */
    public static function verify_ajax_request($nonce) {
        // Check nonce
        if (!self::verify_nonce($nonce)) {
            return new WP_Error('invalid_nonce', __('Security check failed', 'plugin-ranker'));
        }
        
        // Check capabilities
        if (!self::current_user_can_manage()) {
            return new WP_Error('insufficient_permissions', __('Insufficient permissions', 'plugin-ranker'));
        }
        
        return true;
    }
    
    /**
     * Sanitize plugin file name
     * 
     * @param string $plugin_file
     * @return string
     */
    public static function sanitize_plugin_file($plugin_file) {
        // Remove any path traversal attempts
        $plugin_file = str_replace(array('../', '..\\'), '', $plugin_file);
        
        // Sanitize as text field
        $plugin_file = sanitize_text_field($plugin_file);
        
        // Ensure it has the correct format (folder/file.php or file.php)
        if (!preg_match('/^[a-zA-Z0-9_\-\/]+\.php$/', $plugin_file)) {
            return '';
        }
        
        return $plugin_file;
    }
    
    /**
     * Validate plugin file exists and is legitimate
     * 
     * @param string $plugin_file
     * @return bool
     */
    public static function validate_plugin_file($plugin_file) {
        // Sanitize first
        $plugin_file = self::sanitize_plugin_file($plugin_file);
        
        if (empty($plugin_file)) {
            return false;
        }
        
        // Check if plugin exists
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        $all_plugins = get_plugins();
        return isset($all_plugins[$plugin_file]);
    }
    
    /**
     * Sanitize ranking value
     * 
     * @param mixed $rank
     * @return int
     */
    public static function sanitize_rank($rank) {
        $rank = intval($rank);
        return max(0, $rank); // Ensure non-negative
    }
    
    /**
     * Sanitize rankings array
     * 
     * @param array $rankings
     * @return array
     */
    public static function sanitize_rankings($rankings) {
        $sanitized = array();
        
        if (!is_array($rankings)) {
            return $sanitized;
        }
        
        foreach ($rankings as $plugin_file => $rank) {
            $clean_plugin_file = self::sanitize_plugin_file($plugin_file);
            $clean_rank = self::sanitize_rank($rank);
            
            // Only include valid plugin files with positive ranks
            if (!empty($clean_plugin_file) && $clean_rank > 0 && self::validate_plugin_file($clean_plugin_file)) {
                $sanitized[$clean_plugin_file] = $clean_rank;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Check if current request is from admin area
     * 
     * @return bool
     */
    public static function is_admin_request() {
        return is_admin() && !wp_doing_ajax();
    }
    
    /**
     * Check if current request is AJAX
     * 
     * @return bool
     */
    public static function is_ajax_request() {
        return wp_doing_ajax();
    }
    
    /**
     * Check if current page is plugin ranker admin page
     * 
     * @return bool
     */
    public static function is_plugin_ranker_page() {
        $screen = get_current_screen();
        return $screen && $screen->id === 'tools_page_plugin-ranker';
    }
    
    /**
     * Escape output for HTML display
     * 
     * @param string $text
     * @return string
     */
    public static function escape_html($text) {
        return esc_html($text);
    }
    
    /**
     * Escape output for HTML attributes
     * 
     * @param string $text
     * @return string
     */
    public static function escape_attr($text) {
        return esc_attr($text);
    }
    
    /**
     * Escape output for URLs
     * 
     * @param string $url
     * @return string
     */
    public static function escape_url($url) {
        return esc_url($url);
    }
    
    /**
     * Sanitize and validate import data
     * 
     * @param array $data
     * @return array|WP_Error
     */
    public static function validate_import_data($data) {
        // Check if data is array
        if (!is_array($data)) {
            return new WP_Error('invalid_data', __('Import data must be an array', 'plugin-ranker'));
        }
        
        // Check for required fields
        if (!isset($data['plugin_ranker_export']) || !$data['plugin_ranker_export']) {
            return new WP_Error('invalid_format', __('Invalid import file format', 'plugin-ranker'));
        }
        
        if (!isset($data['rankings']) || !is_array($data['rankings'])) {
            return new WP_Error('no_rankings', __('No rankings data found in import file', 'plugin-ranker'));
        }
        
        // Sanitize rankings
        $sanitized_rankings = self::sanitize_rankings($data['rankings']);
        
        return array(
            'rankings' => $sanitized_rankings,
            'original_count' => count($data['rankings']),
            'valid_count' => count($sanitized_rankings)
        );
    }
    
    /**
     * Log security events (if WP_DEBUG is enabled)
     * 
     * @param string $event
     * @param array $context
     */
    public static function log_security_event($event, $context = array()) {
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            return;
        }
        
        $user_id = get_current_user_id();
        $user_login = $user_id ? get_userdata($user_id)->user_login : 'anonymous';
        $ip_address = self::get_client_ip();
        
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'event' => $event,
            'user_id' => $user_id,
            'user_login' => $user_login,
            'ip_address' => $ip_address,
            'context' => $context
        );
        
        error_log('Plugin Ranker Security Event: ' . wp_json_encode($log_entry));
    }
    
    /**
     * Get client IP address
     * 
     * @return string
     */
    private static function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
    }
    
    /**
     * Rate limiting for AJAX requests (basic implementation)
     * 
     * @param string $action
     * @param int $limit
     * @param int $window
     * @return bool
     */
    public static function check_rate_limit($action, $limit = 10, $window = 60) {
        $user_id = get_current_user_id();
        $ip_address = self::get_client_ip();
        $key = 'plugin_ranker_rate_limit_' . $action . '_' . $user_id . '_' . md5($ip_address);
        
        $current_count = get_transient($key);
        
        if ($current_count === false) {
            set_transient($key, 1, $window);
            return true;
        }
        
        if ($current_count >= $limit) {
            self::log_security_event('rate_limit_exceeded', array(
                'action' => $action,
                'count' => $current_count,
                'limit' => $limit
            ));
            return false;
        }
        
        set_transient($key, $current_count + 1, $window);
        return true;
    }
}
