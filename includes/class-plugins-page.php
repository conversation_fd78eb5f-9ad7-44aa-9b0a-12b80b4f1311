<?php
/**
 * Plugins Page Integration Class for Plugin Ranker
 * 
 * Handles modifications to the WordPress Plugins admin page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginRanker_PluginsPage {
    
    /**
     * Initialize hooks for plugins page modifications
     */
    public static function init() {
        // Hook directly without waiting for admin_init
        self::maybe_add_hooks();
    }
    
    /**
     * Add hooks only when on plugins page
     */
    public static function maybe_add_hooks() {
        // Check if we're in admin and on plugins page
        if (!is_admin()) {
            return;
        }

        // For plugins page, we need to hook earlier
        global $pagenow;
        if ($pagenow === 'plugins.php') {
            // Add ranking column
            add_filter('manage_plugins_columns', array(__CLASS__, 'add_ranking_column'));
            add_action('manage_plugins_custom_column', array(__CLASS__, 'display_ranking_column'), 10, 3);

            // Modify plugin list order and filtering
            add_filter('all_plugins', array(__CLASS__, 'sort_plugins_by_ranking'), 10);
            add_filter('all_plugins', array(__CLASS__, 'filter_plugins_by_ranking_status'), 20);

            // Add CSS for ranking column
            add_action('admin_head', array(__CLASS__, 'add_plugins_page_styles'));

            // Add JavaScript for enhanced functionality
            add_action('admin_footer', array(__CLASS__, 'add_plugins_page_scripts'));

            // Add sortable column functionality
            add_filter('manage_plugins_sortable_columns', array(__CLASS__, 'make_ranking_column_sortable'));

            // Add plugin status views (All, Ranked, Unranked)
            add_filter('views_plugins', array(__CLASS__, 'add_ranking_views'));

            // Add admin notice about ranking functionality
            add_action('admin_notices', array(__CLASS__, 'add_ranking_notice'));
        }
    }
    
    /**
     * Add ranking column to plugins table
     * 
     * @param array $columns
     * @return array
     */
    public static function add_ranking_column($columns) {
        // Insert ranking column after the checkbox column
        $new_columns = array();
        
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            
            // Add ranking column after checkbox (cb)
            if ($key === 'cb') {
                $new_columns['plugin_ranking'] = __('Rank', 'plugin-ranker');
            }
        }
        
        return $new_columns;
    }
    
    /**
     * Display content for ranking column
     * 
     * @param string $column_name
     * @param string $plugin_file
     * @param array $plugin_data
     */
    public static function display_ranking_column($column_name, $plugin_file, $plugin_data) {
        if ($column_name === 'plugin_ranking') {
            $ranking = PluginRanker_DataStorage::get_plugin_ranking($plugin_file);
            
            if ($ranking > 0) {
                echo '<span class="plugin-rank-badge rank-' . esc_attr($ranking) . '">';
                echo '<strong>' . esc_html($ranking) . '</strong>';
                echo '</span>';
            } else {
                echo '<span class="plugin-rank-unranked">—</span>';
            }
            
            // Add quick action link to ranking page
            $ranking_url = admin_url('tools.php?page=plugin-ranker');
            echo '<div class="plugin-rank-actions">';
            echo '<a href="' . esc_url($ranking_url) . '" class="plugin-rank-link" title="' . esc_attr__('Manage Rankings', 'plugin-ranker') . '">';
            echo '<span class="dashicons dashicons-sort"></span>';
            echo '</a>';

            // Add quick rank up/down buttons for ranked plugins
            if ($ranking > 0) {
                echo '<div class="plugin-rank-quick-actions">';
                if ($ranking > 1) {
                    echo '<button type="button" class="plugin-rank-up" data-plugin="' . esc_attr($plugin_file) . '" title="' . esc_attr__('Move up', 'plugin-ranker') . '">';
                    echo '<span class="dashicons dashicons-arrow-up-alt2"></span>';
                    echo '</button>';
                }
                echo '<button type="button" class="plugin-rank-down" data-plugin="' . esc_attr($plugin_file) . '" title="' . esc_attr__('Move down', 'plugin-ranker') . '">';
                echo '<span class="dashicons dashicons-arrow-down-alt2"></span>';
                echo '</button>';
                echo '</div>';
            }

            echo '</div>';
        }
    }
    
    /**
     * Sort plugins by ranking
     *
     * @param array $plugins
     * @return array
     */
    public static function sort_plugins_by_ranking($plugins) {
        $orderby = isset($_GET['orderby']) ? $_GET['orderby'] : '';
        $order = isset($_GET['order']) ? $_GET['order'] : 'asc';

        // Get rankings data
        $rankings = PluginRanker_DataStorage::get_plugin_rankings();

        // Debug: Log what we're working with
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Plugin Ranker Debug - Rankings: ' . print_r($rankings, true));
            error_log('Plugin Ranker Debug - Plugin count: ' . count($plugins));
        }

        // Handle ranking column sorting
        if ($orderby === 'plugin_ranking') {
            // Create array with plugin files as keys for easier lookup
            $plugins_with_ranks = array();

            foreach ($plugins as $plugin_file => $plugin_data) {
                $rank = isset($rankings[$plugin_file]) ? intval($rankings[$plugin_file]) : 999;
                $plugins_with_ranks[$plugin_file] = array(
                    'data' => $plugin_data,
                    'rank' => $rank
                );
            }

            // Sort by rank
            uasort($plugins_with_ranks, function($a, $b) use ($order) {
                if ($a['rank'] === $b['rank']) {
                    return strcmp($a['data']['Name'], $b['data']['Name']);
                }

                $result = $a['rank'] - $b['rank'];
                return ($order === 'desc') ? -$result : $result;
            });

            // Extract just the plugin data
            $sorted_plugins = array();
            foreach ($plugins_with_ranks as $plugin_file => $item) {
                $sorted_plugins[$plugin_file] = $item['data'];
            }

            return $sorted_plugins;
        }

        // Default sorting: ranked plugins first, then alphabetical
        if (empty($orderby)) {
            // Separate ranked and unranked plugins
            $ranked_plugins = array();
            $unranked_plugins = array();

            foreach ($plugins as $plugin_file => $plugin_data) {
                $rank = isset($rankings[$plugin_file]) ? intval($rankings[$plugin_file]) : 0;

                if ($rank > 0) {
                    $ranked_plugins[$plugin_file] = $plugin_data;
                    $ranked_plugins[$plugin_file]['_temp_ranking'] = $rank;
                } else {
                    $unranked_plugins[$plugin_file] = $plugin_data;
                }
            }

            // Sort ranked plugins by ranking (ascending)
            uasort($ranked_plugins, function($a, $b) {
                $rank_a = isset($a['_temp_ranking']) ? $a['_temp_ranking'] : 999;
                $rank_b = isset($b['_temp_ranking']) ? $b['_temp_ranking'] : 999;

                if ($rank_a === $rank_b) {
                    return strcmp($a['Name'], $b['Name']);
                }

                return $rank_a - $rank_b;
            });

            // Sort unranked plugins alphabetically
            uasort($unranked_plugins, function($a, $b) {
                return strcmp($a['Name'], $b['Name']);
            });

            // Remove temporary ranking data
            foreach ($ranked_plugins as $plugin_file => $plugin_data) {
                unset($ranked_plugins[$plugin_file]['_temp_ranking']);
            }

            // Combine ranked and unranked plugins
            $sorted_plugins = array_merge($ranked_plugins, $unranked_plugins);

            // Debug: Log the final order
            if (defined('WP_DEBUG') && WP_DEBUG) {
                $plugin_names = array();
                foreach ($sorted_plugins as $file => $data) {
                    $rank = isset($rankings[$file]) ? $rankings[$file] : 'unranked';
                    $plugin_names[] = $data['Name'] . ' (rank: ' . $rank . ')';
                }
                error_log('Plugin Ranker Debug - Final order: ' . implode(', ', $plugin_names));
            }

            return $sorted_plugins;
        }

        return $plugins;
    }
    
    /**
     * Make ranking column sortable
     * 
     * @param array $columns
     * @return array
     */
    public static function make_ranking_column_sortable($columns) {
        $columns['plugin_ranking'] = 'plugin_ranking';
        return $columns;
    }
    
    /**
     * Add CSS styles for plugins page
     */
    public static function add_plugins_page_styles() {
        ?>
        <style type="text/css">
        .column-plugin_ranking {
            width: 80px;
            text-align: center;
        }
        
        .plugin-rank-badge {
            display: inline-block;
            background: #0073aa;
            color: #fff;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            min-width: 20px;
            text-align: center;
            line-height: 1;
        }
        
        .plugin-rank-badge.rank-1 {
            background: #d63638; /* Red for #1 */
        }
        
        .plugin-rank-badge.rank-2 {
            background: #f56e28; /* Orange for #2 */
        }
        
        .plugin-rank-badge.rank-3 {
            background: #f6b026; /* Yellow for #3 */
        }
        
        .plugin-rank-unranked {
            color: #999;
            font-size: 16px;
        }
        
        .plugin-rank-actions {
            margin-top: 4px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .plugin-rank-link {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            padding: 2px;
            border-radius: 2px;
        }

        .plugin-rank-link:hover {
            color: #0073aa;
            background-color: #f0f0f1;
        }

        .plugin-rank-link .dashicons {
            font-size: 14px;
            width: 14px;
            height: 14px;
        }

        .plugin-rank-quick-actions {
            display: flex;
            gap: 2px;
        }

        .plugin-rank-up,
        .plugin-rank-down {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 2px;
            border-radius: 2px;
            font-size: 12px;
        }

        .plugin-rank-up:hover,
        .plugin-rank-down:hover {
            color: #0073aa;
            background-color: #f0f0f1;
        }

        .plugin-rank-up .dashicons,
        .plugin-rank-down .dashicons {
            font-size: 12px;
            width: 12px;
            height: 12px;
        }
        
        /* Highlight ranked plugins */
        .plugins tr[data-plugin-ranked="true"] {
            background-color: #f8f9fa;
        }
        
        .plugins tr[data-plugin-ranked="true"]:hover {
            background-color: #e9ecef;
        }
        
        /* Top ranked plugins get special highlighting */
        .plugins tr[data-plugin-rank="1"] {
            background-color: #fff5f5;
            border-left: 3px solid #d63638;
        }
        
        .plugins tr[data-plugin-rank="2"] {
            background-color: #fff8f0;
            border-left: 3px solid #f56e28;
        }
        
        .plugins tr[data-plugin-rank="3"] {
            background-color: #fffbf0;
            border-left: 3px solid #f6b026;
        }
        
        /* Responsive adjustments */
        @media screen and (max-width: 782px) {
            .column-plugin_ranking {
                display: none;
            }
        }
        </style>
        <?php
    }
    
    /**
     * Add JavaScript for enhanced functionality
     */
    public static function add_plugins_page_scripts() {
        $rankings = PluginRanker_DataStorage::get_plugin_rankings();
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Add data attributes to plugin rows for styling
            var rankings = <?php echo wp_json_encode($rankings); ?>;
            
            $('.plugins tbody tr').each(function() {
                var $row = $(this);
                var pluginFile = '';
                
                // Extract plugin file from the row
                var $checkbox = $row.find('input[type="checkbox"]');
                if ($checkbox.length) {
                    pluginFile = $checkbox.val();
                }
                
                if (pluginFile && rankings[pluginFile]) {
                    var rank = rankings[pluginFile];
                    $row.attr('data-plugin-ranked', 'true');
                    $row.attr('data-plugin-rank', rank);
                }
            });
            
            // Add tooltip to ranking badges
            $('.plugin-rank-badge').each(function() {
                var rank = $(this).text().trim();
                $(this).attr('title', 'Ranked #' + rank);
            });
            
            // Add click handler for ranking links
            $('.plugin-rank-link').on('click', function(e) {
                // Let the link work normally, but could add analytics here
            });

            // Add handlers for quick rank buttons
            $('.plugin-rank-up, .plugin-rank-down').on('click', function(e) {
                e.preventDefault();
                var $button = $(this);
                var pluginFile = $button.data('plugin');
                var direction = $button.hasClass('plugin-rank-up') ? 'up' : 'down';

                // Disable button during request
                $button.prop('disabled', true);

                // Make AJAX request to adjust ranking
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'adjust_plugin_ranking',
                        plugin_file: pluginFile,
                        direction: direction,
                        nonce: '<?php echo wp_create_nonce("plugin_ranker_adjust"); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Reload the page to show updated rankings
                            window.location.reload();
                        } else {
                            alert('Error adjusting ranking: ' + (response.data.message || 'Unknown error'));
                        }
                    },
                    error: function() {
                        alert('Error adjusting ranking. Please try again.');
                    },
                    complete: function() {
                        $button.prop('disabled', false);
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Get plugin ranking for display
     * 
     * @param string $plugin_file
     * @return int
     */
    public static function get_plugin_display_ranking($plugin_file) {
        return PluginRanker_DataStorage::get_plugin_ranking($plugin_file);
    }
    
    /**
     * Check if plugin is ranked
     * 
     * @param string $plugin_file
     * @return bool
     */
    public static function is_plugin_ranked($plugin_file) {
        $ranking = self::get_plugin_display_ranking($plugin_file);
        return $ranking > 0;
    }
    
    /**
     * Get total number of ranked plugins
     * 
     * @return int
     */
    public static function get_ranked_plugins_count() {
        $rankings = PluginRanker_DataStorage::get_plugin_rankings();
        return count(array_filter($rankings, function($rank) {
            return $rank > 0;
        }));
    }
    
    /**
     * Add ranking views to plugins page
     *
     * @param array $views
     * @return array
     */
    public static function add_ranking_views($views) {
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $all_plugins = get_plugins();
        $rankings = PluginRanker_DataStorage::get_plugin_rankings();

        $ranked_count = 0;
        $unranked_count = 0;

        foreach ($all_plugins as $plugin_file => $plugin_data) {
            if (isset($rankings[$plugin_file]) && $rankings[$plugin_file] > 0) {
                $ranked_count++;
            } else {
                $unranked_count++;
            }
        }

        $current_view = isset($_GET['plugin_status']) ? $_GET['plugin_status'] : 'all';

        // Add ranked plugins view
        if ($ranked_count > 0) {
            $ranked_url = add_query_arg(array('plugin_status' => 'ranked'), admin_url('plugins.php'));
            $ranked_class = ($current_view === 'ranked') ? 'current' : '';
            $views['ranked'] = sprintf(
                '<a href="%s" class="%s">%s <span class="count">(%d)</span></a>',
                esc_url($ranked_url),
                $ranked_class,
                __('Ranked', 'plugin-ranker'),
                $ranked_count
            );
        }

        // Add unranked plugins view
        if ($unranked_count > 0) {
            $unranked_url = add_query_arg(array('plugin_status' => 'unranked'), admin_url('plugins.php'));
            $unranked_class = ($current_view === 'unranked') ? 'current' : '';
            $views['unranked'] = sprintf(
                '<a href="%s" class="%s">%s <span class="count">(%d)</span></a>',
                esc_url($unranked_url),
                $unranked_class,
                __('Unranked', 'plugin-ranker'),
                $unranked_count
            );
        }

        return $views;
    }

    /**
     * Filter plugins based on ranking status
     *
     * @param array $plugins
     * @return array
     */
    public static function filter_plugins_by_ranking_status($plugins) {
        $plugin_status = isset($_GET['plugin_status']) ? $_GET['plugin_status'] : '';

        if ($plugin_status === 'ranked' || $plugin_status === 'unranked') {
            $rankings = PluginRanker_DataStorage::get_plugin_rankings();
            $filtered_plugins = array();

            foreach ($plugins as $plugin_file => $plugin_data) {
                $is_ranked = isset($rankings[$plugin_file]) && $rankings[$plugin_file] > 0;

                if (($plugin_status === 'ranked' && $is_ranked) ||
                    ($plugin_status === 'unranked' && !$is_ranked)) {
                    $filtered_plugins[$plugin_file] = $plugin_data;
                }
            }

            return $filtered_plugins;
        }

        return $plugins;
    }

    /**
     * Add admin notice about ranking functionality
     */
    public static function add_ranking_notice() {
        $screen = get_current_screen();

        if ($screen && $screen->id === 'plugins') {
            $ranked_count = self::get_ranked_plugins_count();
            $current_view = isset($_GET['plugin_status']) ? $_GET['plugin_status'] : 'all';

            // Only show notice on main plugins page, not on filtered views
            if ($ranked_count > 0 && $current_view === 'all') {
                $ranking_url = admin_url('tools.php?page=plugin-ranker');
                ?>
                <div class="notice notice-info is-dismissible plugin-ranker-notice">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="flex: 1;">
                            <p style="margin: 0;">
                                <strong><?php _e('Plugin Ranker Active', 'plugin-ranker'); ?></strong> -
                                <?php
                                printf(
                                    __('You have %d ranked plugins. Ranked plugins appear first in the list.', 'plugin-ranker'),
                                    $ranked_count
                                );
                                ?>
                            </p>
                        </div>
                        <div>
                            <a href="<?php echo esc_url($ranking_url); ?>" class="button button-secondary">
                                <?php _e('Manage Rankings', 'plugin-ranker'); ?>
                            </a>
                        </div>
                    </div>
                    <p style="margin: 10px 0 0 0; font-size: 13px; color: #666;">
                        <?php _e('Use the rank column to see plugin positions, or click the sort icon to manage rankings.', 'plugin-ranker'); ?>
                    </p>
                </div>
                <?php
            }
        }
    }
}
