<?php
/**
 * Debug script for Plugin Ranker
 * 
 * Add this to your WordPress site temporarily to debug ranking issues
 * Access via: yoursite.com/wp-content/plugins/plugin-ranker/debug-rankings.php
 */

// Load WordPress
$wp_load_path = '../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once $wp_load_path;
} else {
    die('Could not find WordPress. Make sure this file is in the plugin directory.');
}

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied. You must be an administrator.');
}

// Load plugin classes
require_once 'includes/class-data-storage.php';
require_once 'includes/class-security.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Plugin Ranker Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .debug-section h3 { margin-top: 0; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Plugin Ranker Debug Information</h1>
    
    <div class="debug-section">
        <h3>1. Plugin Constants</h3>
        <pre><?php
        echo "PLUGIN_RANKER_VERSION: " . (defined('PLUGIN_RANKER_VERSION') ? PLUGIN_RANKER_VERSION : 'NOT DEFINED') . "\n";
        echo "PLUGIN_RANKER_PLUGIN_DIR: " . (defined('PLUGIN_RANKER_PLUGIN_DIR') ? PLUGIN_RANKER_PLUGIN_DIR : 'NOT DEFINED') . "\n";
        ?></pre>
    </div>
    
    <div class="debug-section">
        <h3>2. Class Availability</h3>
        <pre><?php
        echo "PluginRanker_DataStorage: " . (class_exists('PluginRanker_DataStorage') ? 'EXISTS' : 'NOT FOUND') . "\n";
        echo "PluginRanker_Security: " . (class_exists('PluginRanker_Security') ? 'EXISTS' : 'NOT FOUND') . "\n";
        echo "PluginRanker_PluginsPage: " . (class_exists('PluginRanker_PluginsPage') ? 'EXISTS' : 'NOT FOUND') . "\n";
        ?></pre>
    </div>
    
    <div class="debug-section">
        <h3>3. Current Rankings Data</h3>
        <pre><?php
        if (class_exists('PluginRanker_DataStorage')) {
            $rankings = PluginRanker_DataStorage::get_plugin_rankings();
            echo "Rankings count: " . count($rankings) . "\n";
            echo "Rankings data:\n";
            print_r($rankings);
        } else {
            echo "Cannot retrieve rankings - class not available\n";
        }
        ?></pre>
    </div>
    
    <div class="debug-section">
        <h3>4. WordPress Options</h3>
        <pre><?php
        $option_value = get_option('plugin_ranker_settings');
        echo "Option exists: " . ($option_value !== false ? 'YES' : 'NO') . "\n";
        echo "Option value:\n";
        print_r($option_value);
        ?></pre>
    </div>
    
    <div class="debug-section">
        <h3>5. Installed Plugins</h3>
        <pre><?php
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        $all_plugins = get_plugins();
        echo "Total plugins: " . count($all_plugins) . "\n";
        echo "Plugin files:\n";
        foreach ($all_plugins as $plugin_file => $plugin_data) {
            echo "- " . $plugin_file . " (" . $plugin_data['Name'] . ")\n";
        }
        ?></pre>
    </div>
    
    <div class="debug-section">
        <h3>6. Plugins with Rankings</h3>
        <pre><?php
        if (class_exists('PluginRanker_DataStorage')) {
            $rankings = PluginRanker_DataStorage::get_plugin_rankings();
            $all_plugins = get_plugins();
            
            echo "Ranked plugins:\n";
            foreach ($rankings as $plugin_file => $rank) {
                $plugin_name = isset($all_plugins[$plugin_file]) ? $all_plugins[$plugin_file]['Name'] : 'PLUGIN NOT FOUND';
                echo "Rank $rank: $plugin_file ($plugin_name)\n";
            }
            
            echo "\nUnranked plugins:\n";
            foreach ($all_plugins as $plugin_file => $plugin_data) {
                if (!isset($rankings[$plugin_file]) || $rankings[$plugin_file] <= 0) {
                    echo "- $plugin_file ({$plugin_data['Name']})\n";
                }
            }
        }
        ?></pre>
    </div>
    
    <div class="debug-section">
        <h3>7. Test Ranking Save/Retrieve</h3>
        <pre><?php
        if (class_exists('PluginRanker_DataStorage')) {
            // Test saving a ranking
            $test_rankings = array(
                'plugin-ranker/plugin-ranker.php' => 1,
                'hello-dolly/hello.php' => 2
            );
            
            echo "Testing save operation...\n";
            $save_result = PluginRanker_DataStorage::update_plugin_rankings($test_rankings);
            echo "Save result: " . ($save_result ? 'SUCCESS' : 'FAILED') . "\n";
            
            echo "Testing retrieve operation...\n";
            $retrieved_rankings = PluginRanker_DataStorage::get_plugin_rankings();
            echo "Retrieved rankings:\n";
            print_r($retrieved_rankings);
            
            // Test individual plugin ranking
            echo "Individual plugin ranking test:\n";
            $individual_rank = PluginRanker_DataStorage::get_plugin_ranking('plugin-ranker/plugin-ranker.php');
            echo "plugin-ranker/plugin-ranker.php rank: $individual_rank\n";
        }
        ?></pre>
    </div>
    
    <div class="debug-section">
        <h3>8. WordPress Environment</h3>
        <pre><?php
        echo "WordPress Version: " . get_bloginfo('version') . "\n";
        echo "PHP Version: " . PHP_VERSION . "\n";
        echo "Current User Can Manage Options: " . (current_user_can('manage_options') ? 'YES' : 'NO') . "\n";
        echo "Is Admin: " . (is_admin() ? 'YES' : 'NO') . "\n";
        echo "WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'ENABLED' : 'DISABLED') . "\n";
        ?></pre>
    </div>
    
    <div class="debug-section">
        <h3>9. Plugin Hooks Test</h3>
        <pre><?php
        echo "Testing if hooks are properly registered...\n";
        
        // Check if our AJAX actions are registered
        echo "AJAX action 'wp_ajax_save_plugin_rankings': ";
        if (has_action('wp_ajax_save_plugin_rankings')) {
            echo "REGISTERED\n";
        } else {
            echo "NOT REGISTERED\n";
        }
        
        echo "AJAX action 'wp_ajax_adjust_plugin_ranking': ";
        if (has_action('wp_ajax_adjust_plugin_ranking')) {
            echo "REGISTERED\n";
        } else {
            echo "NOT REGISTERED\n";
        }
        
        // Check if plugins page hooks are registered
        global $pagenow;
        echo "Current page: " . ($pagenow ? $pagenow : 'UNKNOWN') . "\n";
        
        echo "Filter 'manage_plugins_columns': ";
        if (has_filter('manage_plugins_columns')) {
            echo "HAS FILTERS\n";
        } else {
            echo "NO FILTERS\n";
        }
        
        echo "Filter 'all_plugins': ";
        if (has_filter('all_plugins')) {
            echo "HAS FILTERS\n";
        } else {
            echo "NO FILTERS\n";
        }
        ?></pre>
    </div>
    
    <p><strong>Instructions:</strong></p>
    <ol>
        <li>Check if rankings are being saved correctly (section 3 & 4)</li>
        <li>Verify that plugins are detected properly (section 5)</li>
        <li>Test the save/retrieve functionality (section 7)</li>
        <li>Check if hooks are registered (section 9)</li>
        <li>Delete this file when done debugging for security</li>
    </ol>
    
    <p><em>Remember to delete this debug file when you're done!</em></p>
</body>
</html>
