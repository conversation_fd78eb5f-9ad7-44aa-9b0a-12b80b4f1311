<?php
/**
 * Uninstall script for Plugin Ranker
 * 
 * This file is executed when the plugin is deleted via the WordPress admin.
 * It cleans up all plugin data from the database.
 */

// If uninstall not called from WordPress, exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Define plugin constants if not already defined
if (!defined('PLUGIN_RANKER_VERSION')) {
    define('PLUGIN_RANKER_VERSION', '1.0.0');
}

/**
 * Clean up plugin data
 */
function plugin_ranker_uninstall_cleanup() {
    // Remove plugin options
    delete_option('plugin_ranker_settings');
    
    // Remove any transients (rate limiting, caching, etc.)
    global $wpdb;
    
    // Delete transients with our prefix
    $wpdb->query(
        $wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_plugin_ranker_%'
        )
    );
    
    // Delete transient timeouts
    $wpdb->query(
        $wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_timeout_plugin_ranker_%'
        )
    );
    
    // For multisite, clean up site options if any
    if (is_multisite()) {
        delete_site_option('plugin_ranker_settings');
    }
    
    // Clear any cached data
    wp_cache_flush();
}

// Run cleanup
plugin_ranker_uninstall_cleanup();

// Log uninstall event if debug mode is enabled
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('Plugin Ranker: Plugin uninstalled and data cleaned up');
}
